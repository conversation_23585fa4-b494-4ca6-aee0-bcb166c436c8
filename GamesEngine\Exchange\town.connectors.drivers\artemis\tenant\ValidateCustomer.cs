﻿using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using Puppeteer.EventSourcing;

namespace Connectors.town.connectors.drivers.artemis
{
    public class ValidateCustomer : DGSTenantDriver
    {
		private RestClient _postValidateCustomerClient;

        public ValidateCustomer() : base(Tenant_Actions.Validate)
        {
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_postValidateCustomerClient == null)
			{
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                }

                _postValidateCustomerClient = new RestClient(ServicesUrl);
            }

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    }

                    _postValidateCustomerClient = new RestClient(ServicesUrl);
                }
            }
            var CustomerId = recordSet.Mappings["customerId"];
            var Token = recordSet.Mappings["token"];

            string customerId = CustomerId.AsString;
            if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            string token = Token.AsString;
            if (String.IsNullOrWhiteSpace(token)) throw new ArgumentNullException(nameof(token));

            var result = await ValidateCustomerAsync(now, customerId, token);
			return (T)Convert.ChangeType(result, typeof(T));
		}

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("customerId");
            CustomSettings.AddVariableParameter("token");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }

        private async Task<bool> ValidateCustomerAsync(DateTime now, string customerId, string token)
        {
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            string url = $"/ValidatePlayer";
            string responseString = "";
            string valuesWithHiddenFields = $@"token:{token}";

            try
            {
                Loggers.GetIntance().AccountingServicesDGSValidateCustomer.Debug($@"url:{url} data:{valuesWithHiddenFields}");

                RestRequest request = new RestRequest(url, Method.GET);
                request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                request.AddParameter("token", token);
                var response = await _postValidateCustomerClient.ExecuteAsync(request);
                responseString = response.Content;
                Loggers.GetIntance().AccountingServicesDGSValidateCustomer.Debug($@"response:{responseString}");
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingServicesDGSValidateCustomer.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
            }

            if (string.IsNullOrWhiteSpace(responseString))
            {
                NotifyWarn(nameof(ValidateCustomerAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
            }
            else
            {
                try
                {
                    return Convert.ToBoolean(responseString);
                }
                catch (Exception e)
                {
                    NotifyWarn(nameof(ValidateCustomerAsync), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
                }
            }
            return false;
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
    }
}
