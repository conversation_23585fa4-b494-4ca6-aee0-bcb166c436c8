using GamesEngine.Finance;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;
using static Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization;
using static Connectors.town.connectors.drivers.artemis.ToWin;
using static GamesEngine.Exchange.town.connectors.drivers.artemis.tenant.AccountInfo;

namespace CashierAPI.Controllers
{
	public class DGSArtemisStub : AuthorizeController
	{
		[HttpGet("/GetPayout")]
		[AllowAnonymous]
		public PayoutBody GetPlayerBalance(string playerId, int pick)
		{
			var result = new PayoutBody();
			switch (pick)
			{
				case 2:
					result.payout = new List<ToWinBody>() {
					new ToWinBody() {
						toWin= 30,
						typeName="Straight"
					},
					new ToWinBody() {
						toWin= 15,
						typeName="2-Way"
					}
				};
					break;
				case 3:
					result.payout = new List<ToWinBody>() {
					new ToWinBody() {
						toWin= 300,
						typeName="Straight"
					},
					new ToWinBody() {
						toWin= 300,
						typeName="3-Way"
					},
					new ToWinBody() {
						toWin= 150,
						typeName="6-Way"
					}
				};
					break;
				case 4:
					result.payout = new List<ToWinBody>() {
					new ToWinBody() {
						toWin= 6500,
						typeName="Straight"
					},
					new ToWinBody() {
						toWin= 2250,
						typeName="4-Way"
					},
					new ToWinBody() {
						toWin= 1500,
						typeName="6-Way"
					},
					new ToWinBody() {
						toWin= 750,
						typeName="12-Way"
					}
				};
					break;
				case 5:
					result.payout = new List<ToWinBody>() {
					new ToWinBody() {
						toWin= 90000,
						typeName="Straight"
					},
					new ToWinBody() {
						toWin= 18000,
						typeName="5-Way"
					},
					new ToWinBody() {
						toWin= 9000,
						typeName="10-Way"
					},
					new ToWinBody() {
						toWin= 4500,
						typeName="20-Way"
					},
					new ToWinBody() {
						toWin= 2250,
						typeName="30-Way"
					},
					new ToWinBody() {
						toWin= 1500,
						typeName="60-Way"
					},
					new ToWinBody() {
						toWin= 750,
						typeName="120-Way"
					}
				};
					break;
			}

			return result;
		}

		[HttpGet("/AccountInfo")]
		[AllowAnonymous]
		public AccountInfoBody GetAccountInfo(string name)
		{
			var result = new AccountInfoBody() { idPlayer = 406827, name = name };
			return result;
		}

		static int authorization = 500;
		[HttpPost("/InsertWager")]
		[AllowAnonymous]
		public InsertWagersResponse InsertWagers([FromBody] DebitTransactionBody body)
		{
			var response = new InsertWagersResponse()
			{
				amount = body.amount,
				description = body.description,
				idPlayer = body.idPlayer,
				idTransaction = body.idTransaction,
				referenceId = body.referenceId,
				tickets = new List<ToWinByDrawAndNumber>()
			};
			foreach (var ticket in body.tickets)
			{
				var toWinByDrawAndNumber = new ToWinByDrawAndNumber()
				{
					description = ticket.description,
					draw = ticket.draw,
					drawDate = ticket.drawDate,
					drawHour = ticket.drawHour,
					freePlay = ticket.freePlay,
					number = ticket.number,
					pick = ticket.pick,
					risk = ticket.risk,
					status = ticket.status,
					ticketId = authorization.ToString(),
					toWin = ticket.toWin,
					type = ticket.type
				};
				authorization++;
				response.tickets.Add(toWinByDrawAndNumber);
			}
			return response;
		}

		[HttpPost("/GradingBet")]
		[AllowAnonymous]
		public IActionResult GradeBet([FromBody] WagersUpdateBody body)
		{
			return Ok();
		}

		[HttpPost("/ReGradingBet")]
		[AllowAnonymous]
		public IActionResult ReGradeBet([FromBody] WagersUpdateBody body)
		{
			return Ok();
		}
	}
}
