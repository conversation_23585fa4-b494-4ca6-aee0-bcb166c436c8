﻿using Connectors.town.connectors.drivers;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using log4net;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net.Http;
using Puppeteer.EventSourcing;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors.commons;

namespace town.connectors.drivers.artemis
{
    public abstract class DGSTenantDriver : TenantDriver
    {
		
		protected const int MAX_RETRIES = 3;
		private const float VERSION = 1.0F;
		protected const int FAKE_DOCUMENT_NUMBER = -1;
		protected const int TIME_IN_SECONDS_BEFORE_TOKEN_EXPIRES = 300;
		protected string SystemId { get; set; }
		protected string Password { get; set; }
		protected string ServicesUrl { get; set; }
		private static RestClient _getTokenClient;
		protected static DateTime TokenExpirationTime { get; set; }

		public DGSTenantDriver(Tenant_Actions tenantAction)
			: base(tenantAction, "Artemis", PaymentMethod.ThirdParty, "USD", VERSION)
		{

		}

		public override string Description => "Artemis driver";
		public override string Fabricator => "Ncubo";
		public override DateTime ReleaseDate => new DateTime(2019,01,01);

		internal static async Task RequestAppTokenAsync(string servicesUrl, string systemId, string password)
		{
			while (true)
			{
				DGSProcessorDriver.AppToken = await GetTokenAsync(servicesUrl, systemId, password);
				if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) await Task.Delay(3000);
				else
				{
					UpdateTokenExpirationTime();
					Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($"{nameof(RequestAppTokenAsync)} new token: {DGSProcessorDriver.AppToken} expiration time: {TokenExpirationTime}");
					int waitingTimeInSeconds = Convert.ToInt32((TokenExpirationTime.AddSeconds(-TIME_IN_SECONDS_BEFORE_TOKEN_EXPIRES) - DateTime.UtcNow).TotalSeconds) * 1000;
					await Task.Delay(waitingTimeInSeconds);
				}
			}
		}

		private static void UpdateTokenExpirationTime()
		{
			try
			{
				JwtSecurityTokenHandler tokenHandler = new JwtSecurityTokenHandler();
				var handler = new JwtSecurityTokenHandler();
				var jwtSecurityToken = handler.ReadJwtToken(DGSProcessorDriver.AppToken);
				TokenExpirationTime = jwtSecurityToken.ValidTo;
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesDGSGetToken.Error($"{nameof(UpdateTokenExpirationTime)} new token: {DGSProcessorDriver.AppToken} expiration time: {TokenExpirationTime}", e);
			}
		}
		private static async Task<string> GetTokenAsync(string servicesUrl, string appId, string appKey)
		{
			if (String.IsNullOrWhiteSpace(appId)) throw new ArgumentNullException(nameof(appId));
			if (String.IsNullOrWhiteSpace(appKey)) throw new ArgumentNullException(nameof(appKey));

			if (_getTokenClient == null)
			{
				_getTokenClient = new RestClient(servicesUrl);
			}

			var values = new AppLoginBody()
			{
				appId = appId,
				appKey = appKey
			};
			const string url = "/Auth/Login";
			var jsonString = Commons.ToJson(values);
			string responseString = "";
			string valuesWithHiddenFields = $@"appId:{appId}, appKey:{appKey}";
			try
			{
				Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($@"url:{url} data:{valuesWithHiddenFields}");

				RestRequest request = new RestRequest(url, Method.POST);
				request.AddHeader("Content-Type", "application/json");
				request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
				var response = await _getTokenClient.ExecuteAsync(request);
				responseString = response.Content;

				Loggers.GetIntance().AccountingServicesDGSGetToken.Debug($@"response:{responseString}");
				var responseObj = Commons.FromJson<TokenResponse>(responseString);
				DGSProcessorDriver.AppToken = responseObj.token;
			}
			catch (Exception e)
			{
				Loggers.GetIntance().AccountingServicesDGSGetToken.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
				return string.Empty;
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				//NotifyWarn(nameof(GetTokenAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return string.Empty;
			}

			return DGSProcessorDriver.AppToken;
		}

		int gradedWagersPerChunk = 50;
		public int GradedWagersPerChunk
		{
			get
			{
				return gradedWagersPerChunk;
			}
			set
			{
				if (value < 1)
				{
					throw new Exception($"{nameof(gradedWagersPerChunk)} must be greater than 1. ");
				}
				gradedWagersPerChunk = value;
			}
		}
	}
	public abstract class DGSProcessorDriver : ProcessorDriver
	{
		protected const string SUBSTITUTE_PLAYER_TOKEN = "On6rV1zt3wUS6lqffy7l37FF1tcyofRLIN5pGuwLC9xBgubq6NGbiUA52oAMwhaI";
		protected const string PLAYER_TOKEN_NAME = "tk_1";
		private const float VERSION = 1.0F;
		protected const int FAKE_DOCUMENT_NUMBER = -1;
		protected string SystemId { get; set; }
		protected string Password { get; set; }
		
		internal static string AppToken { get; set; }

		public DGSProcessorDriver(TransactionType transactionType) 
			: base("Artemis", PaymentMethod.ThirdParty, "USD", transactionType, VERSION)
		{

		}

		public override string Description => "Artemis driver";
		public override string Fabricator => "Ncubo";
		public override DateTime ReleaseDate => new DateTime(2019, 01, 01);

	}


	public class AppLoginBody
	{
		public string appId { get; set; }
		public string appKey { get; set; }
	}

	public class TokenResponse
	{
		public string token { get; set; }
	}

	public class PlayerBalanceBody
	{
		public int idPlayer { get; set; }
		public string player { get; set; }
		public decimal curentBalance { get; set; }
		public decimal availBalance { get; set; }
		public decimal amountAtRisk { get; set; }
		public decimal realAvailBalance { get; set; }
		public decimal creditLimit { get; set; }
		public decimal freePlayAmount { get; set; }
		public decimal thisWeek { get; set; }
		public decimal lastWeek { get; set; }
		public decimal bonusPoints { get; set; }
		public int idAgent { get; set; }
		public string agent { get; set; }
		public List<string> betTypeLimit { get; set; }
	}

	public class CreditTransactionBody
	{
		public int idTransaction { get; set; }
		public string idPlayer { get; set; }
		public string description { get; set; }
		public decimal amount { get; set; }
		public string reference { get; set; }
		public decimal fee { get; set; }
		public decimal bonus { get; set; }
	}

	public class DebitTransactionBody
	{
		public int idTransaction { get; set; }
		public string idPlayer { get; set; }
		public string description { get; set; }
		public decimal amount { get; set; }
		public string referenceId { get; set; }
		public decimal fee { get; set; }
		public decimal bonus { get; set; }
		public List<ArtemisToWinByDrawAndNumber> tickets { get; set; }
	}

	public class DebitTransactionResponse
	{
		public int idTransaction { get; set; }
		public string idPlayer { get; set; }
		public string description { get; set; }
		public decimal amount { get; set; }
		public string referenceId { get; set; }
		public decimal fee { get; set; }
		public decimal bonus { get; set; }
		public int toWin { get; set; }
		public List<string> date { get; set; }
		public string code { get; set; }
		public string message { get; set; }
		public List<ArtemisToWinByDrawAndNumber> tickets { get; set; }
	}

	public class WagersCreationResponse
	{
		public string error { get; set; }
	}
	public class WagersCreationBody
	{
		public string ticketNumber { get; set; }
		public WagersCreationCollectionBody wagers { get; set; } = new WagersCreationCollectionBody();
		public void AddWagers(IEnumerable<PostFreeFormWager> pwagers)
		{
			foreach (var wager in pwagers)
			{
				wagers.wagers.Add(new WagerCreationBody()
				{
					betDescription = wager.BetDescription,
					risk = wager.Risk,
					status = wager.Status,
					toWin = wager.ToWin,
					wagerNumber = wager.WagerNumber
				});
			}
		}
	}
	public class WagersCreationCollectionBody
	{
		public List<WagerCreationBody> wagers { get; set; } = new List<WagerCreationBody>();
	}
	public class WagerCreationBody
	{
		public string risk { get; set; }
		public string toWin { get; set; }
		public string betDescription { get; set; }
		public string wagerNumber { get; set; }
		public string status { get; set; }
	}

	public class WagersUpdateBody
	{
        public List<ToWinByDrawAndNumber> tickets { get; set; } = new List<ToWinByDrawAndNumber>();

        public void AddWagers(IEnumerable<PayFragmentsMessage> pwagers)
        {
			foreach (var wager in pwagers)
			{
                tickets.Add(new ToWinByDrawAndNumber()
                {
                    status = wager.Outcome,
                    ticketId = wager.TicketNumber,
                    toWin = string.IsNullOrWhiteSpace(wager.AdjustedWinAmount) ? 0m : decimal.Parse(wager.AdjustedWinAmount)
                });
            }
		}
	}

	public class WagersUpdateCollectionBody
	{
		public List<WagerUpdateBody> wagers { get; set; } = new List<WagerUpdateBody>();
	}

	public class WagerUpdateBody
	{
		public string wagerNumber { get; set; }
		public string status { get; set; }
	}

	public class ArtemisToWinByDrawAndNumber
	{
		public string drawDate { get; set; }
		public string drawHour { get; set; }
		public decimal toWin { get; set; }
		public decimal risk { get; set; }
		public int pick { get; set; }
		public string number { get; set; }
		public string draw { get; set; }
		public string type { get; set; }
		public string description { get; set; }
		public string status { get; set; }
		public string ticketId { get; set; }
		public bool freePlay { get; set; }
	}
}
